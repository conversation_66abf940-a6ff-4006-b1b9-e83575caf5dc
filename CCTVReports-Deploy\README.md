# CCTV Reports Application - Ready to Deploy Package

## Quick Start

1. **Copy this entire folder** to your server
2. **Double-click** `START_CCTV_REPORTS.bat` to run the application
3. **Open browser** and go to `http://localhost:5000`

## What's Included

✅ **Complete Application** - Web API + React Frontend  
✅ **Self-Contained** - No .NET installation required  
✅ **Database Ready** - Configured for SQL Server  
✅ **Production Ready** - Optimized build  

## Files

- `CCTVReportsAPI.exe` - Main application
- `START_CCTV_REPORTS.bat` - Easy startup script
- `appsettings.json` - Configuration file
- `wwwroot/` - Web interface files
- `DEPLOYMENT_INSTRUCTIONS.md` - Detailed setup guide

## Current Configuration

- **Database Server**: `BH-CCTV-REC\SQLEXPRESS`
- **Database**: `MagicLenz`
- **Username**: `CVision`
- **Password**: `CVision`

## Need Help?

See `DEPLOYMENT_INSTRUCTIONS.md` for detailed setup instructions and troubleshooting.

---

**Package Size**: ~100MB  
**Framework**: .NET 8.0 (Self-Contained)  
**Platform**: Windows x64
