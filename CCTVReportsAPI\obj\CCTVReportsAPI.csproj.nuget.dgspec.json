{"format": 1, "restore": {"D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\CCTVReportsAPI.csproj": {}}, "projects": {"D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\CCTVReportsAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\CCTVReportsAPI.csproj", "projectName": "CCTVReportsAPI", "projectPath": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\CCTVReportsAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.16, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.313/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}