# CCTV Reports Application - Deployment Instructions

## Overview
This is a complete, self-contained deployment package for the CCTV Reports application. It includes both the .NET 6 Web API backend and the React frontend in a single executable.

## Prerequisites
- Windows Server/Desktop with .NET 6 runtime (or use the self-contained version included)
- SQL Server with the MagicLenz database
- Network access to the SQL Server instance

## Files Included
- `CCTVReportsAPI.exe` - Main application executable
- `wwwroot/` - React frontend files (served automatically)
- `appsettings.json` - Configuration file
- All required .NET runtime files (self-contained)

## Installation Steps

### 1. Copy Files
Copy the entire `publish` folder to your target server (e.g., `C:\CCTVReports\`)

### 2. Configure Database Connection
Edit `appsettings.json` and update the connection string:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER\\SQLEXPRESS;Database=MagicLenz;User Id=YOUR_USERNAME;Password=YOUR_PASSWORD;TrustServerCertificate=true;"
  },
  "DataSource": {
    "Mode": "Database"
  }
}
```

**Current Configuration:**
- Server: `BH-CCTV-REC\SQLEXPRESS`
- Database: `MagicLenz`
- Username: `CVision`
- Password: `CVision`

### 3. Ensure Database Setup
Make sure the stored procedure `GetMissingRecordingsByDateRange` exists in your MagicLenz database.

### 4. Run the Application
Double-click `CCTVReportsAPI.exe` or run from command line:
```
CCTVReportsAPI.exe
```

### 5. Access the Application
Open your web browser and navigate to:
- **HTTP**: `http://localhost:5000`
- **HTTPS**: `https://localhost:5001`

## Features
- **Full-Screen Interface**: Optimized for monitoring environments
- **Date Range Search**: Default to yesterday 00:00 - today 00:00
- **Real-time Filtering**: Filter by camera name and minimum duration
- **Database Integration**: Direct connection to SQL Server
- **Single Executable**: No separate web server needed

## Troubleshooting

### Application Won't Start
- Check if ports 5000/5001 are available
- Verify .NET 6 runtime is installed (or use self-contained version)
- Check Windows Firewall settings

### Database Connection Issues
- Verify SQL Server is running and accessible
- Test connection string with SQL Server Management Studio
- Check username/password credentials
- Ensure `GetMissingRecordingsByDateRange` stored procedure exists

### Web Interface Issues
- Clear browser cache
- Try different browser
- Check browser console for JavaScript errors

## Configuration Options

### Change Ports
Edit `appsettings.json` to add:
```json
{
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:8080"
      },
      "Https": {
        "Url": "https://localhost:8443"
      }
    }
  }
}
```

### Switch to CSV Mode (if needed)
Change in `appsettings.json`:
```json
{
  "DataSource": {
    "Mode": "CSV",
    "CsvFilePath": "C:\\path\\to\\your\\file.csv"
  }
}
```

## Support
For technical support, contact the development team with:
- Error messages from the console
- Browser console errors (F12 → Console)
- Database connection details
