# CCTV Reports MVP Application

A simple MVP application to query missing CCTV recordings using .NET 6 Web API and React frontend.

## Prerequisites

- .NET 6 SDK
- Node.js (v16 or higher)
- SQL Server
- SQL Server Management Studio (optional)

## Database Setup

1. **Create the stored procedure** in your MagicLenz database:
   ```sql
   -- Run the SQL script in CCTVReportsAPI/SQL/CreateStoredProcedure.sql
   ```

2. **Update connection string** in `CCTVReportsAPI/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=your_server;Database=MagicLenz;Trusted_Connection=true;TrustServerCertificate=true;"
     }
   }
   ```

## Backend Setup (.NET 6 Web API)

1. Navigate to the API directory:
   ```bash
   cd CCTVReportsAPI
   ```

2. Restore packages:
   ```bash
   dotnet restore
   ```

3. Run the API:
   ```bash
   dotnet run
   ```

   The API will be available at: `https://localhost:7154`

## Frontend Setup (React)

1. Navigate to the UI directory:
   ```bash
   cd cctv-reports-ui
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

   The React app will be available at: `http://localhost:5173`

## Usage

1. Open the React application in your browser
2. Select a start date and end date
3. Click "Search" to retrieve missing recordings
4. View the results in the table below

## API Endpoints

- `POST /api/MissingRecordings/search` - Search for missing recordings by date range

## Project Structure

```
cctv-reports/
├── CCTVReportsAPI/          # .NET 6 Web API
│   ├── Controllers/         # API Controllers
│   ├── Models/             # DTOs
│   ├── Services/           # Business Logic
│   └── SQL/               # Database Scripts
└── cctv-reports-ui/        # React Frontend
    └── src/               # React Components
```

## Features

- Date range selection for querying missing recordings
- Responsive table display of results
- Error handling and loading states
- Clean, professional UI design

## Technologies Used

- **Backend**: .NET 6, ASP.NET Core Web API, Microsoft.Data.SqlClient
- **Frontend**: React 18, Vite
- **Database**: SQL Server with stored procedures
