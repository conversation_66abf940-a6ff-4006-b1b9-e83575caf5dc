using Microsoft.Data.SqlClient;
using CCTVReportsAPI.Models;
using System.Data;
using System.Globalization;
using Microsoft.Extensions.Options;

namespace CCTVReportsAPI.Services
{
    public interface IMissingRecordingsService
    {
        Task<List<MissingRecordingDto>> GetMissingRecordingsByDateRangeAsync(DateTime startDate, DateTime endDate);
    }

    public class MissingRecordingsService : IMissingRecordingsService
    {
        private readonly string _connectionString;
        private readonly DataSourceOptions _dataSourceOptions;
        private readonly ILogger<MissingRecordingsService> _logger;

        public MissingRecordingsService(
            IConfiguration configuration,
            IOptions<DataSourceOptions> dataSourceOptions,
            ILogger<MissingRecordingsService> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? throw new ArgumentNullException("Connection string not found");
            _dataSourceOptions = dataSourceOptions.Value;
            _logger = logger;
        }

        public async Task<List<MissingRecordingDto>> GetMissingRecordingsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            if (_dataSourceOptions.Mode.Equals("CSV", StringComparison.OrdinalIgnoreCase))
            {
                return await GetDataFromCsvAsync(startDate, endDate);
            }
            else
            {
                return await GetDataFromDatabaseAsync(startDate, endDate);
            }
        }

        private async Task<List<MissingRecordingDto>> GetDataFromCsvAsync(DateTime startDate, DateTime endDate)
        {
            var recordings = new List<MissingRecordingDto>();

            if (string.IsNullOrEmpty(_dataSourceOptions.CsvFilePath) || !File.Exists(_dataSourceOptions.CsvFilePath))
            {
                _logger.LogWarning("CSV file not found: {FilePath}", _dataSourceOptions.CsvFilePath);
                return recordings;
            }

            try
            {
                // Use FileStream with FileShare.ReadWrite to handle file being open in Excel
                string[] lines;
                using (var fileStream = new FileStream(_dataSourceOptions.CsvFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    var content = await reader.ReadToEndAsync();
                    lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                }

                // Skip header row
                for (int i = 1; i < lines.Length; i++)
                {
                    var fields = ParseCsvLine(lines[i]);
                    if (fields.Length >= 10)
                    {
                        try
                        {
                            var gapStart = DateTime.Parse(fields[2]);
                            var gapEnd = DateTime.Parse(fields[3]);

                            // Filter by date range
                            if (gapStart >= startDate && gapEnd <= endDate)
                            {
                                recordings.Add(new MissingRecordingDto
                                {
                                    ID = int.TryParse(fields[0], out var id) ? id : i,
                                    CameraName = fields[1],
                                    GapStart = gapStart,
                                    GapEnd = gapEnd,
                                    DurationMinutes = decimal.TryParse(fields[4], out var duration) ? duration : 0,
                                    PreviousRecording = string.IsNullOrEmpty(fields[5]) ? null : fields[5],
                                    PreviousDuration = string.IsNullOrEmpty(fields[6]) ? null : fields[6],
                                    NextRecording = string.IsNullOrEmpty(fields[7]) ? null : fields[7],
                                    ReportedDate = DateTime.TryParse(fields[8], out var reportedDate) ? reportedDate : DateTime.Now,
                                    TimeRange = fields.Length > 9 && !string.IsNullOrEmpty(fields[9]) ? fields[9] : null
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning("Error parsing CSV line {LineNumber}: {Error}", i + 1, ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading CSV file: {FilePath}", _dataSourceOptions.CsvFilePath);
            }

            return recordings.OrderByDescending(r => r.GapStart).ThenBy(r => r.CameraName).ToList();
        }

        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var inQuotes = false;
            var currentField = "";

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(currentField.Trim());
                    currentField = "";
                }
                else
                {
                    currentField += c;
                }
            }

            fields.Add(currentField.Trim());
            return fields.ToArray();
        }

        private async Task<List<MissingRecordingDto>> GetDataFromDatabaseAsync(DateTime startDate, DateTime endDate)
        {
            var recordings = new List<MissingRecordingDto>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("GetMissingRecordingsByDateRange", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@StartDate", startDate);
            command.Parameters.AddWithValue("@EndDate", endDate);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                recordings.Add(new MissingRecordingDto
                {
                    ID = reader.GetInt32("ID"),
                    CameraName = reader.GetString("CameraName"),
                    GapStart = reader.GetDateTime("GapStart"),
                    GapEnd = reader.GetDateTime("GapEnd"),
                    DurationMinutes = reader.GetDecimal("DurationMinutes"),
                    PreviousRecording = reader.IsDBNull("PreviousRecording") ? null : reader.GetString("PreviousRecording"),
                    PreviousDuration = reader.IsDBNull("PreviousDuration") ? null : reader.GetString("PreviousDuration"),
                    NextRecording = reader.IsDBNull("NextRecording") ? null : reader.GetString("NextRecording"),
                    ReportedDate = reader.GetDateTime("ReportedDate"),
                    TimeRange = reader.IsDBNull("TimeRange") ? null : reader.GetString("TimeRange")
                });
            }

            return recordings;
        }
    }
}
