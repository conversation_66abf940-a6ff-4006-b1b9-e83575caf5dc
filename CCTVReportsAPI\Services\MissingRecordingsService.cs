using Microsoft.Data.SqlClient;
using CCTVReportsAPI.Models;
using System.Data;
using System.Globalization;
using Microsoft.Extensions.Options;

namespace CCTVReportsAPI.Services
{
    public interface IMissingRecordingsService
    {
        Task<List<MissingRecordingDto>> GetMissingRecordingsByDateRangeAsync(DateTime startDate, DateTime endDate);
    }

    public class MissingRecordingsService : IMissingRecordingsService
    {
        private readonly string _connectionString;
        private readonly DataSourceOptions _dataSourceOptions;
        private readonly ILogger<MissingRecordingsService> _logger;

        public MissingRecordingsService(
            IConfiguration configuration,
            IOptions<DataSourceOptions> dataSourceOptions,
            ILogger<MissingRecordingsService> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? throw new ArgumentNullException("Connection string not found");
            _dataSourceOptions = dataSourceOptions.Value;
            _logger = logger;
        }

        public async Task<List<MissingRecordingDto>> GetMissingRecordingsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            if (_dataSourceOptions.Mode.Equals("CSV", StringComparison.OrdinalIgnoreCase))
            {
                return await GetDataFromCsvAsync(startDate, endDate);
            }
            else
            {
                return await GetDataFromDatabaseAsync(startDate, endDate);
            }
        }

        private async Task<List<MissingRecordingDto>> GetDataFromCsvAsync(DateTime startDate, DateTime endDate)
        {
            var recordings = new List<MissingRecordingDto>();

            if (string.IsNullOrEmpty(_dataSourceOptions.CsvFilePath) || !File.Exists(_dataSourceOptions.CsvFilePath))
            {
                _logger.LogWarning("CSV file not found: {FilePath}", _dataSourceOptions.CsvFilePath);
                return recordings;
            }

            try
            {
                // Use FileStream with FileShare.ReadWrite to handle file being open in Excel
                string[] lines;
                using (var fileStream = new FileStream(_dataSourceOptions.CsvFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    var content = await reader.ReadToEndAsync();
                    lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                }

                _logger.LogInformation("CSV file read successfully. Total lines: {LineCount}", lines.Length);
                if (lines.Length > 0)
                {
                    _logger.LogInformation("Header line: {Header}", lines[0]);
                }

                // Skip header row
                int recordId = 1; // Running ID counter
                for (int i = 1; i < lines.Length; i++)
                {
                    var fields = ParseCsvLine(lines[i]);
                    _logger.LogDebug("Line {LineNumber}: {FieldCount} fields parsed", i + 1, fields.Length);

                    if (fields.Length >= 7) // CSV has 7 fields
                    {
                        try
                        {
                            // CSV format: CameraName, GapStart, GapEnd, DurationMinutes, PreviousDuration, PreviousRecording, NextRecording
                            var gapStart = DateTime.Parse(fields[1]);
                            var gapEnd = DateTime.Parse(fields[2]);

                            _logger.LogDebug("Parsed dates - GapStart: {GapStart}, GapEnd: {GapEnd}", gapStart, gapEnd);
                            _logger.LogDebug("Filter range - StartDate: {StartDate}, EndDate: {EndDate}", startDate, endDate);

                            // Filter by date range
                            if (gapStart >= startDate && gapEnd <= endDate)
                            {
                                _logger.LogDebug("Record matches date range, adding to results");
                                recordings.Add(new MissingRecordingDto
                                {
                                    ID = recordId++, // Use running counter for ID
                                    CameraName = fields[0],
                                    GapStart = gapStart,
                                    GapEnd = gapEnd,
                                    DurationMinutes = decimal.TryParse(fields[3], out var duration) ? duration : 0,
                                    PreviousDuration = string.IsNullOrEmpty(fields[4]) ? null : fields[4],
                                    PreviousRecording = string.IsNullOrEmpty(fields[5]) ? null : fields[5],
                                    NextRecording = string.IsNullOrEmpty(fields[6]) ? null : fields[6],
                                    ReportedDate = DateTime.Now, // Use current date since not in CSV
                                    TimeRange = null // Not available in CSV
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning("Error parsing CSV line {LineNumber}: {Error}", i + 1, ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading CSV file: {FilePath}", _dataSourceOptions.CsvFilePath);
            }

            _logger.LogInformation("CSV processing completed. Found {RecordCount} records matching date range", recordings.Count);
            return recordings.OrderByDescending(r => r.GapStart).ThenBy(r => r.CameraName).ToList();
        }

        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var inQuotes = false;
            var currentField = "";

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(currentField.Trim());
                    currentField = "";
                }
                else
                {
                    currentField += c;
                }
            }

            fields.Add(currentField.Trim());
            return fields.ToArray();
        }

        private async Task<List<MissingRecordingDto>> GetDataFromDatabaseAsync(DateTime startDate, DateTime endDate)
        {
            var recordings = new List<MissingRecordingDto>();

            using var connection = new SqlConnection(_connectionString);
            using var command = new SqlCommand("GetMissingRecordingsByDateRange", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.AddWithValue("@StartDate", startDate);
            command.Parameters.AddWithValue("@EndDate", endDate);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                recordings.Add(new MissingRecordingDto
                {
                    ID = reader.GetInt32("ID"),
                    CameraName = reader.GetString("CameraName"),
                    GapStart = reader.GetDateTime("GapStart"),
                    GapEnd = reader.GetDateTime("GapEnd"),
                    DurationMinutes = reader.GetDecimal("DurationMinutes"),
                    PreviousRecording = reader.IsDBNull("PreviousRecording") ? null : reader.GetString("PreviousRecording"),
                    PreviousDuration = reader.IsDBNull("PreviousDuration") ? null : reader.GetString("PreviousDuration"),
                    NextRecording = reader.IsDBNull("NextRecording") ? null : reader.GetString("NextRecording"),
                    ReportedDate = reader.GetDateTime("ReportedDate"),
                    TimeRange = reader.IsDBNull("TimeRange") ? null : reader.GetString("TimeRange")
                });
            }

            return recordings;
        }
    }
}
