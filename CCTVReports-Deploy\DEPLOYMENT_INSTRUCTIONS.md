# CCTV Reports Application - Self-Contained Deployment Package

## Overview
This is a complete, self-contained deployment package for the CCTV Reports application. It includes both the .NET 8 Web API backend and the React frontend in a single executable package.

**✅ NO .NET RUNTIME REQUIRED** - Everything is included!

## Prerequisites
- Windows Server/Desktop (No .NET runtime required - self-contained)
- SQL Server with the MagicLenz database
- Network access to the SQL Server instance

## Files Included
- `CCTVReportsAPI.exe` - Main application executable
- `wwwroot/` - React frontend files (served automatically)
- `appsettings.json` - Configuration file
- All required .NET 8 runtime files (self-contained)
- All dependencies and libraries

## Installation Steps

### 1. Copy Files
Copy the entire `CCTVReports-Deploy` folder to your target server (e.g., `C:\CCTVReports\`)

### 2. Configure Database Connection
Edit `appsettings.json` and update the connection string:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER\\SQLEXPRESS;Database=MagicLenz;User Id=YOUR_USERNAME;Password=YOUR_PASSWORD;TrustServerCertificate=true;"
  },
  "DataSource": {
    "Mode": "Database"
  }
}
```

**Current Configuration:**
- Server: `BH-CCTV-REC\SQLEXPRESS`
- Database: `MagicLenz`
- Username: `CVision`
- Password: `CVision`

### 3. Ensure Database Setup
Make sure the stored procedure `GetMissingRecordingsByDateRange` exists in your MagicLenz database.

### 4. Run the Application
Double-click `CCTVReportsAPI.exe` or run from command line:
```
CCTVReportsAPI.exe
```

### 5. Access the Application
Open your web browser and navigate to:
- **HTTP**: `http://localhost:5000`
- **HTTPS**: `https://localhost:5001`

## Features
- **Self-Contained**: No .NET runtime installation required
- **Full-Screen Interface**: Optimized for monitoring environments
- **Date Range Search**: Default to yesterday 00:00 - today 00:00
- **Real-time Filtering**: Filter by camera name and minimum duration
- **Database Integration**: Direct connection to SQL Server
- **Single Executable**: No separate web server needed

## Troubleshooting

### Application Won't Start
- Check if ports 5000/5001 are available
- Check Windows Firewall settings
- Run as Administrator if needed

### Database Connection Issues
- Verify SQL Server is running and accessible
- Test connection string with SQL Server Management Studio
- Check username/password credentials
- Ensure `GetMissingRecordingsByDateRange` stored procedure exists

### Web Interface Issues
- Clear browser cache
- Try different browser
- Check browser console for JavaScript errors

## Configuration Options

### Change Ports
Edit `appsettings.json` to add:
```json
{
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:8080"
      },
      "Https": {
        "Url": "https://localhost:8443"
      }
    }
  }
}
```

### Switch to CSV Mode (if needed)
Change in `appsettings.json`:
```json
{
  "DataSource": {
    "Mode": "CSV",
    "CsvFilePath": "C:\\path\\to\\your\\file.csv"
  }
}
```

## Package Details
- **Framework**: .NET 8.0
- **Runtime**: Self-contained (win-x64)
- **Size**: ~100MB (includes all dependencies)
- **No Installation Required**: Just copy and run

## Support
For technical support, contact the development team with:
- Error messages from the console
- Browser console errors (F12 → Console)
- Database connection details
