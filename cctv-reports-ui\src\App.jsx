import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [recordings, setRecordings] = useState([])
  const [filteredRecordings, setFilteredRecordings] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [cameraFilter, setCameraFilter] = useState('')
  const [minDurationFilter, setMinDurationFilter] = useState('')

  // Set default dates on component mount
  useEffect(() => {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    // Format as YYYY-MM-DDTHH:MM for datetime-local input
    const formatDateTime = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}T00:00`
    }

    setStartDate(formatDateTime(yesterday))
    setEndDate(formatDateTime(today))
  }, [])

  // Filter recordings when recordings or filters change
  useEffect(() => {
    let filtered = recordings

    if (cameraFilter) {
      filtered = filtered.filter(record =>
        record.cameraName.toLowerCase().includes(cameraFilter.toLowerCase())
      )
    }

    if (minDurationFilter) {
      const minDuration = parseFloat(minDurationFilter)
      if (!isNaN(minDuration)) {
        filtered = filtered.filter(record => record.durationMinutes >= minDuration)
      }
    }

    setFilteredRecordings(filtered)
  }, [recordings, cameraFilter, minDurationFilter])

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!startDate || !endDate) {
      setError('Please select both start and end dates')
      return
    }

    if (new Date(startDate) > new Date(endDate)) {
      setError('Start date cannot be greater than end date')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('https://localhost:7036/api/MissingRecordings/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startDate: startDate,
          endDate: endDate
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch data')
      }

      const data = await response.json()
      setRecordings(data)
      setFilteredRecordings(data)
    } catch (err) {
      setError('Error fetching data: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>CCTV Missing Recordings Report</h1>
      </header>

      <main className="main-content">
        <form onSubmit={handleSubmit} className="search-form">
          <div className="form-group">
            <label htmlFor="startDate">Start Date:</label>
            <input
              type="datetime-local"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="endDate">End Date:</label>
            <input
              type="datetime-local"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              required
            />
          </div>

          <button type="submit" disabled={loading} className="submit-btn">
            {loading ? 'Searching...' : 'Search'}
          </button>
        </form>

        {error && <div className="error-message">{error}</div>}

        {recordings.length > 0 && (
          <div className="results-section">
            <div className="results-header">
              <h2>Missing Recordings ({filteredRecordings.length} of {recordings.length} shown)</h2>

              <div className="filters">
                <div className="filter-group">
                  <label htmlFor="cameraFilter">Filter by Camera:</label>
                  <input
                    type="text"
                    id="cameraFilter"
                    placeholder="Enter camera name..."
                    value={cameraFilter}
                    onChange={(e) => setCameraFilter(e.target.value)}
                  />
                </div>

                <div className="filter-group">
                  <label htmlFor="minDurationFilter">Min Duration (minutes):</label>
                  <input
                    type="number"
                    id="minDurationFilter"
                    placeholder="0"
                    value={minDurationFilter}
                    onChange={(e) => setMinDurationFilter(e.target.value)}
                  />
                </div>
              </div>
            </div>
            <div className="table-container">
              <table className="results-table">
                <thead>
                  <tr>
                    <th>Camera Name</th>
                    <th>Gap Start</th>
                    <th>Gap End</th>
                    <th>Duration (Minutes)</th>
                    <th>Previous Recording</th>
                    <th>Next Recording</th>
                    <th>Reported Date</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRecordings.map((recording) => (
                    <tr key={recording.id}>
                      <td>{recording.cameraName}</td>
                      <td>{formatDateTime(recording.gapStart)}</td>
                      <td>{formatDateTime(recording.gapEnd)}</td>
                      <td>{recording.durationMinutes}</td>
                      <td>{recording.previousRecording || 'N/A'}</td>
                      <td>{recording.nextRecording || 'N/A'}</td>
                      <td>{formatDateTime(recording.reportedDate)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
