import { useState } from 'react'
import './App.css'

function App() {
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [recordings, setRecordings] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!startDate || !endDate) {
      setError('Please select both start and end dates')
      return
    }

    if (new Date(startDate) > new Date(endDate)) {
      setError('Start date cannot be greater than end date')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('https://localhost:7036/api/MissingRecordings/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startDate: startDate,
          endDate: endDate
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch data')
      }

      const data = await response.json()
      setRecordings(data)
    } catch (err) {
      setError('Error fetching data: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>CCTV Missing Recordings Report</h1>
      </header>

      <main className="main-content">
        <form onSubmit={handleSubmit} className="search-form">
          <div className="form-group">
            <label htmlFor="startDate">Start Date:</label>
            <input
              type="datetime-local"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="endDate">End Date:</label>
            <input
              type="datetime-local"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              required
            />
          </div>

          <button type="submit" disabled={loading} className="submit-btn">
            {loading ? 'Searching...' : 'Search'}
          </button>
        </form>

        {error && <div className="error-message">{error}</div>}

        {recordings.length > 0 && (
          <div className="results-section">
            <h2>Missing Recordings ({recordings.length} found)</h2>
            <div className="table-container">
              <table className="results-table">
                <thead>
                  <tr>
                    <th>Camera Name</th>
                    <th>Gap Start</th>
                    <th>Gap End</th>
                    <th>Duration (Minutes)</th>
                    <th>Previous Recording</th>
                    <th>Next Recording</th>
                    <th>Reported Date</th>
                  </tr>
                </thead>
                <tbody>
                  {recordings.map((recording) => (
                    <tr key={recording.id}>
                      <td>{recording.cameraName}</td>
                      <td>{formatDateTime(recording.gapStart)}</td>
                      <td>{formatDateTime(recording.gapEnd)}</td>
                      <td>{recording.durationMinutes}</td>
                      <td>{recording.previousRecording || 'N/A'}</td>
                      <td>{recording.nextRecording || 'N/A'}</td>
                      <td>{formatDateTime(recording.reportedDate)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
