{"Version": 1, "Hash": "Zf6b28bOlUjGx0G3FnYFR2L8Uj+JX+BwlTPTFQ+RSGk=", "Source": "CCTVReportsAPI", "BasePath": "_content/CCTVReportsAPI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "CCTVReportsAPI\\wwwroot", "Source": "CCTVReportsAPI", "ContentRoot": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\", "BasePath": "_content/CCTVReportsAPI", "Pattern": "**"}], "Assets": [{"Identity": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\assets\\index-5LiaV9Fd.js", "SourceId": "CCTVReportsAPI", "SourceType": "Discovered", "ContentRoot": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\", "BasePath": "_content/CCTVReportsAPI", "RelativePath": "assets/index-5LiaV9Fd.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-5LiaV9Fd.js"}, {"Identity": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\assets\\index-DJkabgu0.css", "SourceId": "CCTVReportsAPI", "SourceType": "Discovered", "ContentRoot": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\", "BasePath": "_content/CCTVReportsAPI", "RelativePath": "assets/index-DJkabgu0.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-DJkabgu0.css"}, {"Identity": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\assets\\index-DyPj_3-q.js", "SourceId": "CCTVReportsAPI", "SourceType": "Discovered", "ContentRoot": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\", "BasePath": "_content/CCTVReportsAPI", "RelativePath": "assets/index-DyPj_3-q.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-DyPj_3-q.js"}, {"Identity": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\index.html", "SourceId": "CCTVReportsAPI", "SourceType": "Discovered", "ContentRoot": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\", "BasePath": "_content/CCTVReportsAPI", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\vite.svg", "SourceId": "CCTVReportsAPI", "SourceType": "Discovered", "ContentRoot": "D:\\rssb\\repos\\cctv-reports\\CCTVReportsAPI\\wwwroot\\", "BasePath": "_content/CCTVReportsAPI", "RelativePath": "vite.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\vite.svg"}]}